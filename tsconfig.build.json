{"compilerOptions": {"declaration": true, "emitDeclarationOnly": true, "outDir": "dist-lib", "rootDir": "src/lib", "module": "ESNext", "target": "ES2021", "jsx": "react-jsx", "allowJs": true, "skipLibCheck": true, "moduleResolution": "bundler", "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "isolatedModules": true}, "include": ["src/lib/**/*"], "exclude": ["src/app", "node_modules"]}