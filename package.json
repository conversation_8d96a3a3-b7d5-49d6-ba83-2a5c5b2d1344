{"version": "1.0.4", "types": "./dist-lib/index.d.ts", "main": "./dist-lib/index.js", "exports": {".": {"import": "./dist-lib/index.js", "types": "./dist-lib/index.d.ts"}}, "files": ["dist-lib"], "type": "module", "scripts": {"dev": "rsbuild dev --config rsbuild.app.config.ts", "build:lib": "rm -rf dist-lib && rslib build --config rslib.config.ts && tsc --project tsconfig.build.json", "publish:lib": "npm publish"}, "private": false, "peerDependencies": {"react-dom": "^18.3.1", "react": "^18.3.1", "@mui/material": "^7.1.1", "@emotion/styled": "^11.14.0", "@emotion/react": "^11.14.0", "react-helmet-async": "^2.0.5", "chroma-js": "^3.1.2"}, "name": "dcl-prefab-components", "devDependencies": {"typescript": "^5.8.3", "react-router-dom": "^7.6.2", "react-dom": "^18.3.1", "react": "^18.3.1", "chroma-js": "^3.1.2", "@types/react-dom": "^18.3.5", "@types/react": "^18.3.18", "@types/chroma-js": "^3.1.1", "@swc/plugin-emotion": "^10.0.0", "@rsbuild/plugin-react": "^1.3.2", "@rsbuild/core": "^1.3.22", "@mui/material": "^7.1.1", "@emotion/styled": "^11.14.0", "@emotion/react": "^11.14.0", "react-helmet-async": "^2.0.5", "@mui/icons-material": "^6.0.0"}, "dependencies": {"@rslib/core": "^0.10.0"}}