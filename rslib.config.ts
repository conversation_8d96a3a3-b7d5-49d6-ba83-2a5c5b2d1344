import { defineConfig } from '@rslib/core';
import { pluginReact } from '@rsbuild/plugin-react';

export default defineConfig({
	plugins: [pluginReact({
		swcReactOptions:{
			runtime: 'automatic',
		}
	})],
	source: {
		entry: {
			index: ['src/lib/**'],
		},
		include: ['src/lib/**/*'],
		exclude: ['src/app/**/*'],
	},
	lib: [
		{
			format: 'esm',
			syntax: 'es2021',
			bundle: false,
		}
	],
	output: {
		target: 'web',
		distPath: { root: './dist-lib' },
		cleanDistPath: true,
		sourceMap: true,
		externals: {
			'react': 'react',
			'react-dom': 'react-dom',
			'@mui/material': '@mui/material',
			'@emotion/react': '@emotion/react',
			'@emotion/styled': '@emotion/styled',
		}
	}
});
