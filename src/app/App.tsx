import './App.css';
import { createBrowserRouter, RouterProvider } from 'react-router-dom';
import LayoutDevClusters from './apps/devclusters/LayoutDevClusters';
import { HelmetProvider } from 'react-helmet-async';

const App = () => {
	
	const router = createBrowserRouter([
		{
			path: '/',
			element: <LayoutDevClusters />,
		},
		{
			path: '/1',
			element: <div>About</div>,
		},
		{
			path: '/2',
			element: <div>About</div>,
		},
	]);
	
	
	return (
		<HelmetProvider>
			<RouterProvider router={router} />
		</HelmetProvider>
	);
};

export default App;
