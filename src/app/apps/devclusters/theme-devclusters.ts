import { createTheme } from '@mui/material';
import { generateAPCAColorPalette } from '@prefab-components/utils/generateAPCAColorPalette';
import React from 'react';

declare module '@mui/material/styles' {
	interface Palette {
		gray: Palette['primary'];
	}
	interface PaletteOptions {
		gray: PaletteOptions['primary'];
	}
	interface PaletteColor {
		100?: string;
		200?: string;
		300?: string;
		400?: string;
		500?: string;
		600?: string;
		700?: string;
		800?: string;
		900?: string;
		950?: string;
	}
	interface SimplePaletteColorOptions {
		100?: string;
		200?: string;
		300?: string;
		400?: string;
		500?: string;
		600?: string;
		700?: string;
		800?: string;
		900?: string;
		950?: string;
	}
	interface TypographyVariants {
		'title': React.CSSProperties;
		'subtitle-lg': React.CSSProperties;
		'subtitle': React.CSSProperties;
		'subtitle-sm': React.CSSProperties;
		'subtitle-xs': React.CSSProperties;
		'body-lg': React.CSSProperties;
		'body': React.CSSProperties;
		'body-sm': React.CSSProperties;
		'link-lg': React.CSSProperties;
		'link': React.CSSProperties;
		'link-sm': React.CSSProperties;
		'caption': React.CSSProperties;
		'label': React.CSSProperties;
	}
	interface  TypographyVariantsOptions {
		'title': React.CSSProperties;
		'subtitle-lg': React.CSSProperties;
		'subtitle': React.CSSProperties;
		'subtitle-sm': React.CSSProperties;
		'subtitle-xs': React.CSSProperties;
		'body-lg': React.CSSProperties;
		'body': React.CSSProperties;
		'body-sm': React.CSSProperties;
		'link-lg': React.CSSProperties;
		'link': React.CSSProperties;
		'link-sm': React.CSSProperties;
		'caption': React.CSSProperties;
		'label': React.CSSProperties;
	}
}

declare module '@mui/material/Typography' {
	interface TypographyPropsVariantOverrides {
		'title': true;
		'subtitle-lg': true;
		'subtitle': true;
		'subtitle-sm': true;
		'subtitle-xs': true;
		'body-lg': true;
		'body': true;
		'body-sm': true;
		'link-lg': true;
		'link': true;
		'link-sm': true;
		'caption': true;
		'label': true;
	}
}

const primaryScale = generateAPCAColorPalette('#003366')
const secondaryScale = generateAPCAColorPalette('#4C7DDE')
const grayScale = generateAPCAColorPalette('#9e9e9e')
const warningScale = generateAPCAColorPalette('#ffc03c')
const errorScale = generateAPCAColorPalette('#e00e00')
const infoScale = generateAPCAColorPalette('#0077b6')
const successScale = generateAPCAColorPalette('#00b300')

const theme = createTheme({
	cssVariables:true,
	palette: {
		mode: 'light',
		primary: {
			...primaryScale,
			light: '#355dac',
			main: '#033598',
			dark: '#02256a',
			50: '#ECF2FD',
			100: '#D1DFFA',
			300: '#6C96E9',
			600: '#033598',
			700: '#022A7A',
		},
		secondary: {
			...secondaryScale,
		},
		gray: {
			...grayScale
		},
		warning: {
			...warningScale
		},
		error:{
			...errorScale
		},
		info: {
			...infoScale
		},
		success: {
			...successScale
		}
	},
	typography: {
		allVariants: {
			fontFamily: "'Inter', sans-serif",
		},
		title: {
			fontSize: 'clamp(1.5rem, 0.233vw + 1.439rem, 1.625rem)', // 24px - 26px
			fontWeight: 600,
			letterSpacing: '0.03em',
			color: primaryScale[950],
		},
		'subtitle-lg': {
			fontSize: 'clamp(1.375rem, 0.233vw + 1.314rem, 1.5rem)', // 22px - 24px
			fontWeight: 600,
			letterSpacing: '0.02em',
			color: primaryScale[900],
		},
		subtitle: {
			fontSize: 'clamp(1.25rem, 0.233vw + 1.189rem, 1.375rem)', // 20px - 22px
			fontWeight: 600,
			letterSpacing: '0.01em',
			color: primaryScale[900],
		},
		'subtitle-sm': {
			fontSize: 'clamp(1.125rem, 0.233vw + 1.064rem, 1.25rem)', // 18px - 20px
			fontWeight: 600,
			color: primaryScale[900],
		},
		'subtitle-xs': {
			fontSize: 'clamp(1rem, 0.233vw + 0.939rem, 1.125rem)', // 16px - 18px
			fontWeight: 600,
			color: grayScale[800],
		},
		'body-lg': {
			fontSize: 'clamp(1rem, 0.233vw + 0.939rem, 1.125rem)', // 16px - 18px
			color: grayScale[900],
		},
		body: {
			fontSize: 'clamp(0.875rem, 0.233vw + 0.814rem, 1rem)', // 14px - 16px
			color: grayScale[900],
		},
		'body-sm': {
			fontSize: 'clamp(0.75rem, 0.233vw + 0.689rem, 0.875rem)', // 12px - 14px
			color: grayScale[900],
		},
		'link-lg': {
			fontSize: 'clamp(1rem, 0.233vw + 0.939rem, 1.125rem)', // 16px - 18px
			textDecoration: 'underline',
			cursor: 'pointer',
			color: grayScale[900],
		},
		link: {
			fontSize: 'clamp(0.875rem, 0.233vw + 0.814rem, 1rem)', // 14px - 16px
			textDecoration: 'underline',
			cursor: 'pointer',
			color: grayScale[900],
		},
		'link-sm': {
			fontSize: 'clamp(0.75rem, 0.233vw + 0.689rem, 0.875rem)', // 12px - 14px
			textDecoration: 'underline',
			cursor: 'pointer',
			color: grayScale[900],
		},
		caption: {
			fontSize: 'clamp(0.688rem, 0.233vw + 0.626rem, 0.813rem)', // 11px - 13px
			color: grayScale[700],
		},
		label: {
			fontSize: 'clamp(0.688rem, 0.233vw + 0.626rem, 0.813rem)', // 11px - 13px
			fontWeight: 500,
			color: grayScale[700],
			lineHeight: 2,
		},
	},
	components: {
		MuiTypography: {
			defaultProps: {
				variantMapping: {
					'title': 'h1',
					'subtitle-lg': 'h2',
					'subtitle': 'h3',
					'subtitle-sm': 'h4',
					'body': 'p',
					'body-lg': 'p',
					'body-sm': 'p',
					'link-lg': 'a',
					'link': 'a',
					'link-sm': 'a',
					'caption': 'span',
					'label': 'span',
				},
			},
		},
		MuiLink: {
			styleOverrides: {
				root: {
					textDecoration: 'underline',
					color: 'inherit',
					cursor: 'pointer',
					margin: '0 4px',
				},
			},
			defaultProps: {
				// component: LinkBehavior,
			},
		},
		MuiCard: {
			styleOverrides: {
				root: {
					borderRadius: '24px',
					boxShadow: 'none',
					border: '1px solid',
					borderColor: grayScale[50],
					padding: '24px',
				},
			},
		},
	},
})

export default theme
