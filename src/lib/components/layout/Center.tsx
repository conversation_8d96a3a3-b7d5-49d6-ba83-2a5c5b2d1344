import { Container } from '@mui/material';
import React from 'react';


interface CenterProps {
	className?: string;
	children: React.ReactNode;
	sx?: any;
}

export default function Center({ children, sx, className }: CenterProps) {
	return (
		<Container
			className={className}
			sx={[
				{
					display: 'flex',
					flexDirection: 'column',
					justifyContent: 'center',
					alignItems: 'center',
					minHeight: '100vh',
				},
				...(Array.isArray(sx) ? sx : [sx]),
			]}
		>
			{children}
		</Container>
	);
}
