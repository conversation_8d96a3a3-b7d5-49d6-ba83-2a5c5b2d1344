import Stack from '@mui/material/Stack';
import { ButtonIcon } from '../buttons/ButtonIcon';
import React from 'react';
import Tooltip from '@mui/material/Tooltip';
import { IconButtonProps } from '@mui/material/IconButton';

export interface ActionButtonProps extends IconButtonProps {
	icon: React.ReactNode;
	tooltip?: string;
	onClick?: (event: React.MouseEvent<HTMLButtonElement>) => void;
}

export interface IconActionsProps {
	actions: {
		[key: string]: ActionButtonProps;
	};
}

export default function IconActions({ actions }: IconActionsProps) {
	return (
		<Stack
			className='actions'
			sx={{
				opacity: 0.4,
				transition: 'opacity 0.2s ease-in',
			}}
			direction="row"
			gap={0.5}
		>
			{Object.keys(actions).map((key) => (
				<Tooltip
					key={key}
					title={actions[key].tooltip}
				>
					<ButtonIcon
						key={key}
						{...actions[key]}
					>
						{actions[key].icon}
					</ButtonIcon>
				</Tooltip>
			))}
		</Stack>
	);
}
