import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>u, MenuItem } from "@mui/material";
import React, { SetStateAction } from "react";
import MoreVertIcon from "@mui/icons-material/MoreVert";
import { ButtonIcon } from "../buttons/ButtonIcon";

export interface ContextMenuProps {
  menu: {
    [key: string]: {
      label: string;
      onClick: () => void;
    };
  };
}

export default function ContextMenu({ menu }: ContextMenuProps) {
  const [anchorEl, setAnchorEl] = React.useState(null);
  const open = Boolean(anchorEl);
  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget as unknown as SetStateAction<null>);
  };
  const handleClose = () => {
    setAnchorEl(null);
  };

  return (
    <>
      <ButtonIcon
        className="menu-button"
        sx={{
          opacity: 0.4,
		  transition: 'opacity 0.s ease-in',
        }}
        aria-controls={open ? "basic-menu" : undefined}
        aria-haspopup="true"
        aria-expanded={open ? "true" : undefined}
        onClick={handleClick}
      >
        <MoreVertIcon  />
      </ButtonIcon>
      <Menu
        id="basic-menu"
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
        slotProps={{
          list: {
            "aria-labelledby": "basic-button",
          },
        }}
      >
        {Object.keys(menu).map((key) => (
          <MenuItem key={key} onClick={menu[key].onClick}>
            {menu[key].label}
          </MenuItem>
        ))}
      </Menu>
    </>
  );
}
