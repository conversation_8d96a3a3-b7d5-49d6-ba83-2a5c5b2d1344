import chroma from 'chroma-js';

/**
 * Generates an accessible color palette using perceptual lightness steps (LCH),
 * returning the result in a structured object format commonly used in UI libraries.
 * This approach is inspired by the principles in the Wildbit Accessible Palette article,
 * focusing on varying the L* component of LCH while maintaining hue and chroma.
 *
 * The function generates 11 colors corresponding to keys 50-950 and maps
 * descriptive keys (light, main, dark, text, textLight) and adaptive
 * text colors (contrastText, contrastTextLight, contrastTextDark) to specific
 * colors from this generated list or based on contrast checks.
 *
 * @param {string} baseColor - The starting color (e.g., '#ff0000', 'rgb(0, 255, 0)', 'blue').
 * @param {number[]} lightnessRange - An array [minL, maxL] defining the target L* range (0-100)
 * for the lightest and darkest colors in the palette scale.
 * Defaults to [10, 90] to provide a reasonable range.
 * @returns {{ [key: string]: string }} An object containing the palette colors keyed by numbers (50-950)
 * and descriptive names, including adaptive text colors. Returns an empty object on error.
 */
export function generateAPCAColorPalette(baseColor: string = '#033598', lightnessRange: number[] = [15, 85]): {
	[key: string]: string
} {
	try {
		const color = chroma(baseColor);
		const [_, c, h] = color.lch(); // Get L*, Chroma, Hue
		
		// Ensure lightness range is valid
		const minL = Math.max(0, Math.min(lightnessRange[0], lightnessRange[1]));
		const maxL = Math.min(100, Math.max(lightnessRange[0], lightnessRange[1]));
		
		// We generate 11 colors for the 50-950 scale (indices 0-10)
		const numberOfColors = 11;
		
		// Create start and end colors in LCH space using the base color's chroma and hue
		const startColor = chroma(minL, c, h, 'lch');
		const endColor = chroma(maxL, c, h, 'lch');
		
		// Create a scale interpolating between the start and end colors in LCH mode.
		const scale = chroma.scale([endColor, startColor]).mode('hsl');
		
		// Generate the 11 colors from the scale as hex strings
		const paletteArray = scale.colors(numberOfColors).map(col => chroma(col).hex());
		
		// Map the generated colors to the desired keys.
		// Based on your example indices and common UI palette patterns:
		return {
			base: baseColor,
			50: paletteArray[0],
			100: paletteArray[1],
			200: paletteArray[2],
			300: paletteArray[3],
			400: paletteArray[4],
			500: paletteArray[5],
			600: paletteArray[6],
			700: paletteArray[7],
			800: paletteArray[8],
			900: paletteArray[9],
			950: paletteArray[10],
			
			background: chroma.mix(paletteArray[0], '#fff', 0.9).hex(),
			
			// Map descriptive keys based on common UI libraries or your specific needs.
			// Using indices corresponding to numerical keys:
			light: paletteArray[2], // Often corresponds to 300 or 400
			main: baseColor, // Often corresponds to 500
			dark: paletteArray[6], // Often corresponds to 700 or 800
			text: paletteArray[9], // Often a dark color, like 900 or 950
			textLight: paletteArray[7], // Often a light color, like 100 or 200
			textDark: paletteArray[10], // Often a dark color, like 800 or 900
			
			// Add adaptive text colors based on contrast checks
			// Using the getContrastTextColor helper
			contrastTextLight:
				Math.abs(chroma.contrast(baseColor, paletteArray[4])) >
				Math.abs(chroma.contrast(baseColor, paletteArray[6]))
					? paletteArray[4]
					: paletteArray[6],
			contrastText:
				Math.abs(chroma.contrast(baseColor, paletteArray[2])) >
				Math.abs(chroma.contrast(baseColor, paletteArray[8]))
					? paletteArray[2]
					: paletteArray[8],
			contrastTextDark:
				Math.abs(chroma.contrast(baseColor, paletteArray[0])) >
				Math.abs(chroma.contrast(baseColor, paletteArray[10]))
					? chroma.mix(paletteArray[0], '#fff', 0.5).hex()
					: chroma.mix(paletteArray[10], '#000', 0.5).hex(),
		};
	} catch (error) {
		console.error('Error generating palette:', error);
		// Return an empty object or handle the error as needed
		return {};
	}
}
