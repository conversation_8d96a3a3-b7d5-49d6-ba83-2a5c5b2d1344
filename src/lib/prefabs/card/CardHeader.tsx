import React from 'react';
import IconActions, { ActionButtonProps } from '../../components/control/IconActions';
import Checkbox, { CheckboxProps } from '@mui/material/Checkbox';
import Grid from '@mui/material/Grid';
import Typography from '@mui/material/Typography';
import ContextMenu from '../../components/control/ContextMenu';

export interface CardHeaderProps {
	title: React.ReactNode;
	description?: React.ReactNode;
	checked?: boolean;
	onChecked?: (event: React.ChangeEvent<HTMLInputElement>) => void;
	actions?: {
		[key: string]: ActionButtonProps;
	};
	menu?: {
		[key: string]: {
			label: string;
			onClick: () => void;
		};
	};
	components?: {
		title?: React.ComponentType;
		description?: React.ComponentType;
		checkbox?: React.ComponentType<CheckboxProps>;
	};
}

export default function CardHeader({
	                                   title,
	                                   description,
	                                   checked,
	                                   onChecked,
	                                   menu,
	                                   components,
	                                   actions,
                                   }: CardHeaderProps) {
	return (
		<Grid
			container
			columnSpacing={0.5}
		>
			<Grid
				size={'grow'}
				sx={{
					display: 'flex',
					alignItems: 'center',
					overflow: 'hidden',
				}}
			>
				{components?.title ? (
					<components.title />
				) : (
					<Typography
						variant="subtitle-sm"
						sx={{
							overflow: 'hidden',
							textOverflow: 'ellipsis',
							whiteSpace: 'nowrap',
						}}
					>
						{title}
					</Typography>
				)}
			</Grid>
			{actions && (
				<Grid
					size={'auto'}
					sx={{
						display: 'flex',
						justifyContent: 'flex-end',
					}}
				>
					<IconActions actions={actions} />
				</Grid>
			)}
			{menu && (
				<Grid
					size={'auto'}
					sx={{
						display: 'flex',
						justifyContent: 'flex-end',
					}}
				>
					<ContextMenu menu={menu} />
				</Grid>
			)}
			{(checked || onChecked) && (
				<Grid
					size={'auto'}
					sx={{
						display: 'flex',
						justifyContent: 'flex-end',
					}}
				>
					{components?.checkbox ? (
						<components.checkbox
							checked={checked}
							onChange={onChecked}
						/>
					) : (
						<Checkbox
							sx={{
								'&.MuiCheckbox-root': {
									width: '36px',
									height: '36px',
									borderRadius: 2,
								},
							}}
							checked={checked}
							onChange={onChecked}
						/>
					)}
				</Grid>
			)}
			<Grid size={12}>
				{components?.description ? (
					<components.description />
				) : (
					<Typography
						className="description"
						variant="body-sm"
						color="gray.500"
					>
						{description}
					</Typography>
				)}
			</Grid>
		</Grid>
	);
}
