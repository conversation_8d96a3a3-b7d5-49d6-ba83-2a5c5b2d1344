import React from "react";
import Stack, { StackProps } from "@mui/material/Stack";

export interface CardBodyProps extends StackProps {
  children: React.ReactNode;
  maxHeight?: string;
  expand?: boolean;
}

export default function CardBody({
  children,
  maxHeight,
  expand = false,
  sx,
}: CardBodyProps) {
  return (
    <Stack
      className={expand ? "expand" : ""}
      sx={[
        {
          position: "relative",
          pt: 1.5,
          height: maxHeight ? maxHeight : "auto",
          overflowY: expand ? "auto" : "hidden",
          msOverflowStyle: "none",
          scrollbarWidth: "none",
          transition: "height 0.3s ease",
          interpolateSize: "allow-keywords",
          "&::-webkit-scrollbar": {
            display: "none",
          },
          "&.expand": {
            height: "auto",
          },
          "&::after": {
            content: '""',
            position: "absolute",
            bottom: 0,
            left: 0,
            right: 0,
            height: "40%",
            background: !maxHeight
              ? "none"
              : "linear-gradient(to top, white 10%, transparent)",
            display: expand ? "none" : "block",
            pointerEvents: "none",
          },
        },
        ...(Array.isArray(sx) ? sx : [sx]),
      ]}
    >
      {children}
    </Stack>
  );
}
