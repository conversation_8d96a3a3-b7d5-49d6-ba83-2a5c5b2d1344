import React from 'react';
import MuiCard, { CardProps as MuiCardProps } from '@mui/material/Card';

export interface CardProps extends MuiCardProps {
	children: React.ReactNode;
}

export default function Card({ children, sx, ...props }: CardProps) {
	return (
		<MuiCard
			sx={[
				{
					borderRadius: '16px',
					boxShadow: 'none',
					border: '1px solid',
					borderColor: 'gray.200',
					padding: '24px',
					// modify .actions and .menu-button opacity on hover card to 0.8
					'&:hover': {
						'.actions': {
							opacity: 0.8,
						},
						'.menu-button': {
							opacity: 0.8,
						},
					},

				},
				...(Array.isArray(sx) ? sx : [sx]),
			]}
			{...props}
		>
			{children}
		</MuiCard>
	);
}
