import React from 'react';
import Paper from '@mui/material/Paper';
import TableContainer, { TableContainerProps } from '@mui/material/TableContainer';
import MuiTable from '@mui/material/Table';

interface TableProps extends TableContainerProps {
	children: React.ReactNode;
}

export function Table({ children, sx, ...props }: TableProps) {
	
	return (
		<TableContainer
			component={Paper}
			sx={[
				{
					minHeight: '100%',
					border: 'none',
					borderRadius: 3,
					boxShadow: 'none',
				},
				...(Array.isArray(sx) ? sx : [sx]),
			]}
		>
			<MuiTable
				sx={{
					minWidth: 650,
					border: 'none',
				}}
			>
				{children}
			</MuiTable>
		</TableContainer>
	);
}
