import MuiTableHead, { TableHeadProps as MuiTableHeadProps } from '@mui/material/TableHead';
import React from 'react';
import { TableCell, TableRow } from '@mui/material';
import Grid from '@mui/material/Grid';
import Typography from '@mui/material/Typography';
import IconActions, { ActionButtonProps } from '@prefab-components/components/control/IconActions';


interface TableHeadProps extends MuiTableHeadProps {
	title?: string;
	description?: string;
	colSpan?: number;
	children?: React.ReactNode;
	actions?: {
		[key: string]: ActionButtonProps;
	};
}

export default function TableHead({ children, title, description, actions, colSpan, sx, ...props }: TableHeadProps) {
	
	if (children) {
		return (
			<MuiTableHead {...props}>
				{children}
			</MuiTableHead>
		);
	}
	
	return (
		<MuiTableHead
			sx={[
				{
					'& .MuiTableCell-root': {
						borderTop: 'none',
						borderBottom: 'none',
						background: 'white',
					},
				},
				...(Array.isArray(sx) ? sx : [sx]),
			]}
		>
			<TableRow>
				<TableCell
					colSpan={colSpan}
					sx={{
						p: 3,
					}}
				>
					<Grid container>
						<Grid size={12}
							sx={{
								display: 'flex',
								alignItems: 'center',
							}}
						>
							<Typography
								variant="subtitle-sm"
								sx={{
									fontFamily: 'Inter, Avenir, Helvetica, Arial, sans-serif',
									overflow: 'hidden',
									textOverflow: 'ellipsis',
									whiteSpace: 'nowrap',
								}}
							>
								{title}
							</Typography>
						</Grid>
						{actions && (
							<Grid
								size={'auto'}
								sx={{
									display: 'flex',
									justifyContent: 'flex-end',
								}}
							>
								<IconActions actions={actions} />
							</Grid>
						)}
						<Grid size={12}
							sx={{
								display: 'flex',
								alignItems: 'center',
							}}
						>
							<Typography
								variant="body-sm"
								color="gray.600"
								fontWeight={400}
								sx={{
									fontFamily: 'Inter, Avenir, Helvetica, Arial, sans-serif',
								}}
							>
								{description}
							</Typography>
						</Grid>
					</Grid>
				</TableCell>
			</TableRow>
		</MuiTableHead>
	);
}
