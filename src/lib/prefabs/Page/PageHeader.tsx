import Grid from "@mui/material/Grid";
import { Avatar, Typography } from '@mui/material';
import { Helmet } from 'react-helmet-async';
import React from 'react';

interface PageHeaderProps {
	title: string;
	description: string;
	avatar?: {
		label: string;
		src: string;
	};
	breadcrumbs?: {
		label: string;
		href: string;
	}[];
	meta?: React.ReactNode;
	hiddenTitle?: boolean;
	docs?: string;
	sx?: any;
}

export default function PageHeader({ title, description, avatar, breadcrumbs, meta, hiddenTitle = false, docs, sx } : PageHeaderProps) {
	return (
		<Grid
			sx={[
				{
					mb: 1,
				},
				...(Array.isArray(sx) ? sx : [sx]),
			]}
			container
			columnSpacing={2}>
			<Helmet>
				<title>{title}</title>
				<meta
					name="description"
					content={description}
				/>
				<meta
					name="og:title"
					content={title}
				/>
				<meta
					name="og:description"
					content={description}
				/>
			</Helmet>
			<Grid
				size={10}
				sx={{ display: 'flex', alignItems: 'center' }}>
				{/*<PageBreadcrumbs breadcrumbs={breadcrumbs} />*/}
			</Grid>
			<Grid
				size={2}
				sx={{ display: 'flex', alignItems: 'center', justifyContent: 'flex-end', gap: 2 }}>
				{meta}
				{/*<DocumentationLink docs={docs} />*/}
			</Grid>
			<Grid
				size={12}
				sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
				{avatar && (
					<Avatar
						className="page-avatar"
						alt={avatar?.label}
						src={avatar?.src}
						sx={{
							width: 64,
							height: 64,
							bgcolor: 'primary.50',
							color: 'primary.600',
							fontWeight: 500,
							fontSize: '24px',
							lineHeight: '32px',
						}}>
						{avatar?.label}
					</Avatar>
				)}
				{!hiddenTitle && (
					<Typography
						className="page-title"
						variant="title"
						sx={{
							fontSize: '30px',
							color: 'gray.800',
							fontWeight: 600,
							letterSpacing: '0.03em',
						}}>
						{title}
					</Typography>
				)}
			</Grid>
			<Grid size={12}>
				<Typography
					className="page-description"
					variant="body"
					sx={{
						color: 'gray.500',
						mt: 1,
					}}>
					{description}
				</Typography>
			</Grid>
		</Grid>
	)
}
