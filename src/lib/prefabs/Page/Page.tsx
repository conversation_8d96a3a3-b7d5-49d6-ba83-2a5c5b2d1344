import { Container, ContainerProps} from '@mui/material';
import PageHeader from './PageHeader';

import Stack from '@mui/material/Stack';
import { StackProps } from '@mui/material/Stack';

interface PageProps extends ContainerProps {}

export default function Page({ maxWidth = 'xl', sx, children, ...props } : PageProps) {
	return (
		<Container
			maxWidth={maxWidth}
			sx={[
				{
					my: {
						xs: '80px',
						md: 6,
					},
				},
				...(Array.isArray(sx) ? sx : [sx]),
			]}
			{...props}>
			{children}
		</Container>
	)
}

Page.Header = PageHeader;

export function PageBody({ children, sx, ...props }: StackProps) {
	return (
		<Stack
			sx={[
				{
					py: 4,
					gap: 2,
				},
				...(Array.isArray(sx) ? sx : [sx]),
			]}
			{...props}>
			{children}
		</Stack>
	)
}
