import React from 'react';
export * from './prefabs/card';
export * from './prefabs/Page/Page';
declare module '@mui/material/styles' {
    interface Palette {
        gray: Palette['primary'];
    }
    interface PaletteOptions {
        gray: PaletteOptions['primary'];
    }
    interface PaletteColor {
        100?: string;
        200?: string;
        300?: string;
        400?: string;
        500?: string;
        600?: string;
        700?: string;
        800?: string;
        900?: string;
        950?: string;
    }
    interface SimplePaletteColorOptions {
        100?: string;
        200?: string;
        300?: string;
        400?: string;
        500?: string;
        600?: string;
        700?: string;
        800?: string;
        900?: string;
        950?: string;
    }
    interface TypographyVariants {
        'title': React.CSSProperties;
        'subtitle-lg': React.CSSProperties;
        'subtitle': React.CSSProperties;
        'subtitle-sm': React.CSSProperties;
        'subtitle-xs': React.CSSProperties;
        'body-lg': React.CSSProperties;
        'body': React.CSSProperties;
        'body-sm': React.CSSProperties;
        'link-lg': React.CSSProperties;
        'link': React.CSSProperties;
        'link-sm': React.CSSProperties;
        'caption': React.CSSProperties;
        'label': React.CSSProperties;
    }
    interface TypographyVariantsOptions {
        'title': React.CSSProperties;
        'subtitle-lg': React.CSSProperties;
        'subtitle': React.CSSProperties;
        'subtitle-sm': React.CSSProperties;
        'subtitle-xs': React.CSSProperties;
        'body-lg': React.CSSProperties;
        'body': React.CSSProperties;
        'body-sm': React.CSSProperties;
        'link-lg': React.CSSProperties;
        'link': React.CSSProperties;
        'link-sm': React.CSSProperties;
        'caption': React.CSSProperties;
        'label': React.CSSProperties;
    }
}
declare module '@mui/material/Typography' {
    interface TypographyPropsVariantOverrides {
        'title': true;
        'subtitle-lg': true;
        'subtitle': true;
        'subtitle-sm': true;
        'subtitle-xs': true;
        'body-lg': true;
        'body': true;
        'body-sm': true;
        'link-lg': true;
        'link': true;
        'link-sm': true;
        'caption': true;
        'label': true;
    }
}
