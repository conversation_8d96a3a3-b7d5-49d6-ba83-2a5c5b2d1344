{"version": 3, "file": "utils/generateAPCAColorPalette.js", "sources": ["webpack://dcl-prefab-components/./src/lib/utils/generateAPCAColorPalette.ts"], "sourcesContent": ["import chroma from 'chroma-js';\n\n/**\n * Generates an accessible color palette using perceptual lightness steps (LCH),\n * returning the result in a structured object format commonly used in UI libraries.\n * This approach is inspired by the principles in the Wildbit Accessible Palette article,\n * focusing on varying the L* component of LCH while maintaining hue and chroma.\n *\n * The function generates 11 colors corresponding to keys 50-950 and maps\n * descriptive keys (light, main, dark, text, textLight) and adaptive\n * text colors (contrastText, contrastTextLight, contrastTextDark) to specific\n * colors from this generated list or based on contrast checks.\n *\n * @param {string} baseColor - The starting color (e.g., '#ff0000', 'rgb(0, 255, 0)', 'blue').\n * @param {number[]} lightnessRange - An array [minL, maxL] defining the target L* range (0-100)\n * for the lightest and darkest colors in the palette scale.\n * Defaults to [10, 90] to provide a reasonable range.\n * @returns {{ [key: string]: string }} An object containing the palette colors keyed by numbers (50-950)\n * and descriptive names, including adaptive text colors. Returns an empty object on error.\n */\nexport function generateAPCAColorPalette(baseColor: string = '#033598', lightnessRange: number[] = [15, 85]): {\n\t[key: string]: string\n} {\n\ttry {\n\t\tconst color = chroma(baseColor);\n\t\tconst [_, c, h] = color.lch(); // Get L*, Chroma, Hue\n\t\t\n\t\t// Ensure lightness range is valid\n\t\tconst minL = Math.max(0, Math.min(lightnessRange[0], lightnessRange[1]));\n\t\tconst maxL = Math.min(100, Math.max(lightnessRange[0], lightnessRange[1]));\n\t\t\n\t\t// We generate 11 colors for the 50-950 scale (indices 0-10)\n\t\tconst numberOfColors = 11;\n\t\t\n\t\t// Create start and end colors in LCH space using the base color's chroma and hue\n\t\tconst startColor = chroma(minL, c, h, 'lch');\n\t\tconst endColor = chroma(maxL, c, h, 'lch');\n\t\t\n\t\t// Create a scale interpolating between the start and end colors in LCH mode.\n\t\tconst scale = chroma.scale([endColor, startColor]).mode('hsl');\n\t\t\n\t\t// Generate the 11 colors from the scale as hex strings\n\t\tconst paletteArray = scale.colors(numberOfColors).map(col => chroma(col).hex());\n\t\t\n\t\t// Map the generated colors to the desired keys.\n\t\t// Based on your example indices and common UI palette patterns:\n\t\treturn {\n\t\t\tbase: baseColor,\n\t\t\t50: paletteArray[0],\n\t\t\t100: paletteArray[1],\n\t\t\t200: paletteArray[2],\n\t\t\t300: paletteArray[3],\n\t\t\t400: paletteArray[4],\n\t\t\t500: paletteArray[5],\n\t\t\t600: paletteArray[6],\n\t\t\t700: paletteArray[7],\n\t\t\t800: paletteArray[8],\n\t\t\t900: paletteArray[9],\n\t\t\t950: paletteArray[10],\n\t\t\t\n\t\t\tbackground: chroma.mix(paletteArray[0], '#fff', 0.9).hex(),\n\t\t\t\n\t\t\t// Map descriptive keys based on common UI libraries or your specific needs.\n\t\t\t// Using indices corresponding to numerical keys:\n\t\t\tlight: paletteArray[2], // Often corresponds to 300 or 400\n\t\t\tmain: baseColor, // Often corresponds to 500\n\t\t\tdark: paletteArray[6], // Often corresponds to 700 or 800\n\t\t\ttext: paletteArray[9], // Often a dark color, like 900 or 950\n\t\t\ttextLight: paletteArray[7], // Often a light color, like 100 or 200\n\t\t\ttextDark: paletteArray[10], // Often a dark color, like 800 or 900\n\t\t\t\n\t\t\t// Add adaptive text colors based on contrast checks\n\t\t\t// Using the getContrastTextColor helper\n\t\t\tcontrastTextLight:\n\t\t\t\tMath.abs(chroma.contrast(baseColor, paletteArray[4])) >\n\t\t\t\tMath.abs(chroma.contrast(baseColor, paletteArray[6]))\n\t\t\t\t\t? paletteArray[4]\n\t\t\t\t\t: paletteArray[6],\n\t\t\tcontrastText:\n\t\t\t\tMath.abs(chroma.contrast(baseColor, paletteArray[2])) >\n\t\t\t\tMath.abs(chroma.contrast(baseColor, paletteArray[8]))\n\t\t\t\t\t? paletteArray[2]\n\t\t\t\t\t: paletteArray[8],\n\t\t\tcontrastTextDark:\n\t\t\t\tMath.abs(chroma.contrast(baseColor, paletteArray[0])) >\n\t\t\t\tMath.abs(chroma.contrast(baseColor, paletteArray[10]))\n\t\t\t\t\t? chroma.mix(paletteArray[0], '#fff', 0.5).hex()\n\t\t\t\t\t: chroma.mix(paletteArray[10], '#000', 0.5).hex(),\n\t\t};\n\t} catch (error) {\n\t\tconsole.error('Error generating palette:', error);\n\t\t// Return an empty object or handle the error as needed\n\t\treturn {};\n\t}\n}\n"], "names": ["generateAPCAColorPalette", "baseColor", "lightnessRange", "color", "chroma", "_", "c", "h", "minL", "Math", "maxL", "numberOfColors", "startColor", "endColor", "scale", "paletteArray", "col", "error", "console"], "mappings": ";AAoBO,SAASA,yBAAyBC,YAAoB,SAAS,EAAEC,iBAA2B;IAAC;IAAI;CAAG;IAG1G,IAAI;QACH,MAAMC,QAAQC,UAAOH;QACrB,MAAM,CAACI,GAAGC,GAAGC,EAAE,GAAGJ,MAAM,GAAG;QAG3B,MAAMK,OAAOC,KAAK,GAAG,CAAC,GAAGA,KAAK,GAAG,CAACP,cAAc,CAAC,EAAE,EAAEA,cAAc,CAAC,EAAE;QACtE,MAAMQ,OAAOD,KAAK,GAAG,CAAC,KAAKA,KAAK,GAAG,CAACP,cAAc,CAAC,EAAE,EAAEA,cAAc,CAAC,EAAE;QAGxE,MAAMS,iBAAiB;QAGvB,MAAMC,aAAaR,UAAOI,MAAMF,GAAGC,GAAG;QACtC,MAAMM,WAAWT,UAAOM,MAAMJ,GAAGC,GAAG;QAGpC,MAAMO,QAAQV,UAAAA,KAAY,CAAC;YAACS;YAAUD;SAAW,EAAE,IAAI,CAAC;QAGxD,MAAMG,eAAeD,MAAM,MAAM,CAACH,gBAAgB,GAAG,CAACK,CAAAA,MAAOZ,UAAOY,KAAK,GAAG;QAI5E,OAAO;YACN,MAAMf;YACN,IAAIc,YAAY,CAAC,EAAE;YACnB,KAAKA,YAAY,CAAC,EAAE;YACpB,KAAKA,YAAY,CAAC,EAAE;YACpB,KAAKA,YAAY,CAAC,EAAE;YACpB,KAAKA,YAAY,CAAC,EAAE;YACpB,KAAKA,YAAY,CAAC,EAAE;YACpB,KAAKA,YAAY,CAAC,EAAE;YACpB,KAAKA,YAAY,CAAC,EAAE;YACpB,KAAKA,YAAY,CAAC,EAAE;YACpB,KAAKA,YAAY,CAAC,EAAE;YACpB,KAAKA,YAAY,CAAC,GAAG;YAErB,YAAYX,UAAAA,GAAU,CAACW,YAAY,CAAC,EAAE,EAAE,QAAQ,KAAK,GAAG;YAIxD,OAAOA,YAAY,CAAC,EAAE;YACtB,MAAMd;YACN,MAAMc,YAAY,CAAC,EAAE;YACrB,MAAMA,YAAY,CAAC,EAAE;YACrB,WAAWA,YAAY,CAAC,EAAE;YAC1B,UAAUA,YAAY,CAAC,GAAG;YAI1B,mBACCN,KAAK,GAAG,CAACL,UAAAA,QAAe,CAACH,WAAWc,YAAY,CAAC,EAAE,KACnDN,KAAK,GAAG,CAACL,UAAAA,QAAe,CAACH,WAAWc,YAAY,CAAC,EAAE,KAChDA,YAAY,CAAC,EAAE,GACfA,YAAY,CAAC,EAAE;YACnB,cACCN,KAAK,GAAG,CAACL,UAAAA,QAAe,CAACH,WAAWc,YAAY,CAAC,EAAE,KACnDN,KAAK,GAAG,CAACL,UAAAA,QAAe,CAACH,WAAWc,YAAY,CAAC,EAAE,KAChDA,YAAY,CAAC,EAAE,GACfA,YAAY,CAAC,EAAE;YACnB,kBACCN,KAAK,GAAG,CAACL,UAAAA,QAAe,CAACH,WAAWc,YAAY,CAAC,EAAE,KACnDN,KAAK,GAAG,CAACL,UAAAA,QAAe,CAACH,WAAWc,YAAY,CAAC,GAAG,KACjDX,UAAAA,GAAU,CAACW,YAAY,CAAC,EAAE,EAAE,QAAQ,KAAK,GAAG,KAC5CX,UAAAA,GAAU,CAACW,YAAY,CAAC,GAAG,EAAE,QAAQ,KAAK,GAAG;QAClD;IACD,EAAE,OAAOE,OAAO;QACfC,QAAQ,KAAK,CAAC,6BAA6BD;QAE3C,OAAO,CAAC;IACT;AACD"}