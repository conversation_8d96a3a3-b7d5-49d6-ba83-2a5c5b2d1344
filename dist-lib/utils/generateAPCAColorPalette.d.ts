/**
 * Generates an accessible color palette using perceptual lightness steps (LCH),
 * returning the result in a structured object format commonly used in UI libraries.
 * This approach is inspired by the principles in the Wildbit Accessible Palette article,
 * focusing on varying the L* component of LCH while maintaining hue and chroma.
 *
 * The function generates 11 colors corresponding to keys 50-950 and maps
 * descriptive keys (light, main, dark, text, textLight) and adaptive
 * text colors (contrastText, contrastTextLight, contrastTextDark) to specific
 * colors from this generated list or based on contrast checks.
 *
 * @param {string} baseColor - The starting color (e.g., '#ff0000', 'rgb(0, 255, 0)', 'blue').
 * @param {number[]} lightnessRange - An array [minL, maxL] defining the target L* range (0-100)
 * for the lightest and darkest colors in the palette scale.
 * Defaults to [10, 90] to provide a reasonable range.
 * @returns {{ [key: string]: string }} An object containing the palette colors keyed by numbers (50-950)
 * and descriptive names, including adaptive text colors. Returns an empty object on error.
 */
export declare function generateAPCAColorPalette(baseColor?: string, lightnessRange?: number[]): {
    [key: string]: string;
};
