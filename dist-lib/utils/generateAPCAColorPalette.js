import chroma_js from "chroma-js";
function generateAPCAColorPalette(baseColor = '#033598', lightnessRange = [
    15,
    85
]) {
    try {
        const color = chroma_js(baseColor);
        const [_, c, h] = color.lch();
        const minL = Math.max(0, Math.min(lightnessRange[0], lightnessRange[1]));
        const maxL = Math.min(100, Math.max(lightnessRange[0], lightnessRange[1]));
        const numberOfColors = 11;
        const startColor = chroma_js(minL, c, h, 'lch');
        const endColor = chroma_js(maxL, c, h, 'lch');
        const scale = chroma_js.scale([
            endColor,
            startColor
        ]).mode('hsl');
        const paletteArray = scale.colors(numberOfColors).map((col)=>chroma_js(col).hex());
        return {
            base: baseColor,
            50: paletteArray[0],
            100: paletteArray[1],
            200: paletteArray[2],
            300: paletteArray[3],
            400: paletteArray[4],
            500: paletteArray[5],
            600: paletteArray[6],
            700: paletteArray[7],
            800: paletteArray[8],
            900: paletteArray[9],
            950: paletteArray[10],
            background: chroma_js.mix(paletteArray[0], '#fff', 0.9).hex(),
            light: paletteArray[2],
            main: baseColor,
            dark: paletteArray[6],
            text: paletteArray[9],
            textLight: paletteArray[7],
            textDark: paletteArray[10],
            contrastTextLight: Math.abs(chroma_js.contrast(baseColor, paletteArray[4])) > Math.abs(chroma_js.contrast(baseColor, paletteArray[6])) ? paletteArray[4] : paletteArray[6],
            contrastText: Math.abs(chroma_js.contrast(baseColor, paletteArray[2])) > Math.abs(chroma_js.contrast(baseColor, paletteArray[8])) ? paletteArray[2] : paletteArray[8],
            contrastTextDark: Math.abs(chroma_js.contrast(baseColor, paletteArray[0])) > Math.abs(chroma_js.contrast(baseColor, paletteArray[10])) ? chroma_js.mix(paletteArray[0], '#fff', 0.5).hex() : chroma_js.mix(paletteArray[10], '#000', 0.5).hex()
        };
    } catch (error) {
        console.error('Error generating palette:', error);
        return {};
    }
}
export { generateAPCAColorPalette };

//# sourceMappingURL=generateAPCAColorPalette.js.map