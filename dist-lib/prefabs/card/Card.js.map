{"version": 3, "file": "prefabs/card/Card.js", "sources": ["webpack://dcl-prefab-components/./src/lib/prefabs/card/Card.tsx"], "sourcesContent": ["import React from 'react';\nimport MuiCard, { CardProps as MuiCardProps } from '@mui/material/Card';\n\nexport interface CardProps extends MuiCardProps {\n\tchildren: React.ReactNode;\n}\n\nexport default function Card({ children, sx, ...props }: CardProps) {\n\treturn (\n\t\t<MuiCard\n\t\t\tsx={[\n\t\t\t\t{\n\t\t\t\t\tborderRadius: '16px',\n\t\t\t\t\tboxShadow: 'none',\n\t\t\t\t\tborder: '1px solid',\n\t\t\t\t\tborderColor: 'gray.50',\n\t\t\t\t\tpadding: '24px',\n\t\t\t\t\t\n\t\t\t\t},\n\t\t\t\t...(Array.isArray(sx) ? sx : [sx]),\n\t\t\t]}\n\t\t\t{...props}\n\t\t>\n\t\t\t{children}\n\t\t</MuiCard>\n\t);\n}\n"], "names": ["Card", "children", "sx", "props", "MuiCard", "Array"], "mappings": ";;;AAOe,SAASA,UAAK,EAAEC,QAAQ,EAAEC,EAAE,EAAE,GAAGC,OAAkB;IACjE,OAAO,WAAP,GACC,IAACC,MAAOA;QACP,IAAI;YACH;gBACC,cAAc;gBACd,WAAW;gBACX,QAAQ;gBACR,aAAa;gBACb,SAAS;YAEV;eACIC,MAAM,OAAO,CAACH,MAAMA,KAAK;gBAACA;aAAG;SACjC;QACA,GAAGC,KAAK;kBAERF;;AAGJ"}