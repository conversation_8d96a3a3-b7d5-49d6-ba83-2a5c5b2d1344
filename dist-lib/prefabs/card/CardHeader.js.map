{"version": 3, "file": "prefabs/card/CardHeader.js", "sources": ["webpack://dcl-prefab-components/./src/lib/prefabs/card/CardHeader.tsx"], "sourcesContent": ["import React from 'react';\nimport IconActions, { ActionButtonProps } from '../../components/control/IconActions';\nimport Checkbox, { CheckboxProps } from '@mui/material/Checkbox';\nimport Grid from '@mui/material/Grid';\nimport Typography from '@mui/material/Typography';\nimport ContextMenu from '../../components/control/ContextMenu';\n\nexport interface CardHeaderProps {\n\ttitle: React.ReactNode;\n\tdescription?: React.ReactNode;\n\tchecked?: boolean;\n\tonChecked?: (event: React.ChangeEvent<HTMLInputElement>) => void;\n\tactions?: {\n\t\t[key: string]: ActionButtonProps;\n\t},\n\tmenu?: {\n\t\t[key: string]: {\n\t\t\tlabel: string;\n\t\t\tonClick: () => void;\n\t\t};\n\t};\n\tcomponents?: {\n\t\ttitle?: React.ComponentType;\n\t\tdescription?: React.ComponentType;\n\t\tcheckbox?: React.ComponentType<CheckboxProps>;\n\t};\n}\n\nexport default function CardHeader(\n\t{\n\t\ttitle,\n\t\tdescription,\n\t\tchecked,\n\t\tonChecked,\n\t\tmenu,\n\t\tcomponents,\n\t\tactions,\n\t}: CardHeaderProps) {\n\treturn (\n\t\t<Grid\n\t\t\tcontainer\n\t\t\tcolumnSpacing={0.5}\n\t\t>\n\t\t\t<Grid\n\t\t\t\tsize={'grow'}\n\t\t\t\tsx={{\n\t\t\t\t\tdisplay: 'flex',\n\t\t\t\t\talignItems: 'center',\n\t\t\t\t\toverflow: 'hidden',\n\t\t\t\t}}\n\t\t\t>\n\t\t\t\t{components?.title\n\t\t\t\t\t? <components.title />\n\t\t\t\t\t: <Typography\n\t\t\t\t\t\tvariant=\"subtitle-sm\"\n\t\t\t\t\t\tsx={{\n\t\t\t\t\t\t\toverflow: 'hidden',\n\t\t\t\t\t\t\ttextOverflow: 'ellipsis',\n\t\t\t\t\t\t\twhiteSpace: 'nowrap',\n\t\t\t\t\t\t}}\n\t\t\t\t\t>\n\t\t\t\t\t\t{title}\n\t\t\t\t\t</Typography>\n\t\t\t\t}\n\t\t\t</Grid>\n\t\t\t{actions && (\n\t\t\t\t<Grid\n\t\t\t\t\tsize={'auto'}\n\t\t\t\t\tsx={{\n\t\t\t\t\t\tdisplay: 'flex',\n\t\t\t\t\t\tjustifyContent: 'flex-end',\n\t\t\t\t\t}}\n\t\t\t\t>\n\t\t\t\t\t<IconActions actions={actions} />\n\t\t\t\t</Grid>\n\t\t\t)}\n\t\t\t{menu && (\n\t\t\t\t<Grid\n\t\t\t\t\tsize={'auto'}\n\t\t\t\t\tsx={{\n\t\t\t\t\t\tdisplay: 'flex',\n\t\t\t\t\t\tjustifyContent: 'flex-end',\n\t\t\t\t\t}}\n\t\t\t\t>\n\t\t\t\t\t<ContextMenu menu={menu} />\n\t\t\t\t</Grid>\n\t\t\t)}\n\t\t\t{(checked || onChecked) && (\n\t\t\t\t<Grid\n\t\t\t\t\tsize={'auto'}\n\t\t\t\t\tsx={{\n\t\t\t\t\t\tdisplay: 'flex',\n\t\t\t\t\t\tjustifyContent: 'flex-end',\n\t\t\t\t\t}}\n\t\t\t\t>\n\t\t\t\t\t{components?.checkbox\n\t\t\t\t\t\t? <components.checkbox\n\t\t\t\t\t\t\tchecked={checked}\n\t\t\t\t\t\t\tonChange={onChecked}\n\t\t\t\t\t\t/>\n\t\t\t\t\t\t: <Checkbox\n\t\t\t\t\t\t\tsx={{\n\t\t\t\t\t\t\t\t'&.MuiCheckbox-root': {\n\t\t\t\t\t\t\t\t\twidth: '36px',\n\t\t\t\t\t\t\t\t\theight: '36px',\n\t\t\t\t\t\t\t\t\tborderRadius: 2,\n\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t}}\n\t\t\t\t\t\t\tchecked={checked}\n\t\t\t\t\t\t\tonChange={onChecked}\n\t\t\t\t\t\t/>\n\t\t\t\t\t}\n\t\t\t\t</Grid>\n\t\t\t)}\n\t\t\t<Grid\n\t\t\t\tsize={12}\n\t\t\t>\n\t\t\t\t{components?.description\n\t\t\t\t\t? <components.description />\n\t\t\t\t\t: <Typography\n\t\t\t\t\t\tvariant=\"body-sm\"\n\t\t\t\t\t\tcolor=\"gray.500\"\n\t\t\t\t\t>\n\t\t\t\t\t\t{description}\n\t\t\t\t\t</Typography>\n\t\t\t\t}\n\t\t\t</Grid>\n\t\t</Grid>\n\t);\n}\n"], "names": ["<PERSON><PERSON><PERSON><PERSON>", "title", "description", "checked", "onChecked", "menu", "components", "actions", "Grid", "Typography", "IconActions", "ContextMenu", "Checkbox"], "mappings": ";;;;;;;AA4Be,SAASA,WACvB,EACCC,KAAK,EACLC,WAAW,EACXC,OAAO,EACPC,SAAS,EACTC,IAAI,EACJC,UAAU,EACVC,OAAO,EACU;IAClB,OAAO,WAAP,GACC,KAACC,MAAIA;QACJ,WAAS;QACT,eAAe;;0BAEf,IAACA,MAAIA;gBACJ,MAAM;gBACN,IAAI;oBACH,SAAS;oBACT,YAAY;oBACZ,UAAU;gBACX;0BAECF,AAAAA,CAAAA,QAAAA,aAAAA,KAAAA,IAAAA,WAAY,KAAK,AAAD,IAAC,cACf,IAACA,WAAW,KAAK,sBACjB,IAACG,YAAUA;oBACZ,SAAQ;oBACR,IAAI;wBACH,UAAU;wBACV,cAAc;wBACd,YAAY;oBACb;8BAECR;;;YAIHM,WAAW,WAAXA,GACA,IAACC,MAAIA;gBACJ,MAAM;gBACN,IAAI;oBACH,SAAS;oBACT,gBAAgB;gBACjB;0BAEA,kBAACE,aAAWA;oBAAC,SAASH;;;YAGvBF,QAAQ,WAARA,GACA,IAACG,MAAIA;gBACJ,MAAM;gBACN,IAAI;oBACH,SAAS;oBACT,gBAAgB;gBACjB;0BAEA,kBAACG,aAAWA;oBAAC,MAAMN;;;YAGnBF,CAAAA,WAAWC,SAAQ,mBACpB,IAACI,MAAIA;gBACJ,MAAM;gBACN,IAAI;oBACH,SAAS;oBACT,gBAAgB;gBACjB;0BAECF,AAAAA,CAAAA,QAAAA,aAAAA,KAAAA,IAAAA,WAAY,QAAQ,AAAD,IAAC,cAClB,IAACA,WAAW,QAAQ;oBACrB,SAASH;oBACT,UAAUC;mCAET,IAACQ,UAAQA;oBACV,IAAI;wBACH,sBAAsB;4BACrB,OAAO;4BACP,QAAQ;4BACR,cAAc;wBACf;oBACD;oBACA,SAAST;oBACT,UAAUC;;;0BAKd,IAACI,MAAIA;gBACJ,MAAM;0BAELF,AAAAA,CAAAA,QAAAA,aAAAA,KAAAA,IAAAA,WAAY,WAAW,AAAD,IAAC,cACrB,IAACA,WAAW,WAAW,sBACvB,IAACG,YAAUA;oBACZ,SAAQ;oBACR,OAAM;8BAELP;;;;;AAMP"}