import { jsx } from "react/jsx-runtime";
import "react";
import Stack from "@mui/material/Stack";
function CardBody({ children, maxHeight, expand = false }) {
    return /*#__PURE__*/ jsx(Stack, {
        className: expand ? 'expand' : '',
        sx: {
            position: 'relative',
            pt: 1.5,
            height: maxHeight ? maxHeight : 'auto',
            overflowY: expand ? 'auto' : 'hidden',
            msOverflowStyle: 'none',
            scrollbarWidth: 'none',
            transition: 'height 0.3s ease',
            interpolateSize: 'allow-keywords',
            '&::-webkit-scrollbar': {
                display: 'none'
            },
            '&.expand': {
                height: 'auto'
            },
            '&::after': {
                content: '""',
                position: 'absolute',
                bottom: 0,
                left: 0,
                right: 0,
                height: '40%',
                background: maxHeight ? 'linear-gradient(to top, white 10%, transparent)' : 'none',
                display: expand ? 'none' : 'block',
                pointerEvents: 'none'
            }
        },
        children: children
    });
}
export { CardBody as default };

//# sourceMappingURL=CardBody.js.map