{"version": 3, "file": "prefabs/card/CardBody.js", "sources": ["webpack://dcl-prefab-components/./src/lib/prefabs/card/CardBody.tsx"], "sourcesContent": ["import React from 'react';\nimport Stack from '@mui/material/Stack';\n\nexport interface CardBodyProps {\n\tchildren: React.ReactNode;\n\tmaxHeight?: string;\n\texpand?: boolean;\n}\n\nexport default function CardBody({ children, maxHeight, expand = false }: CardBodyProps) {\n\treturn (\n\t\t<Stack\n\t\t\tclassName={expand ? 'expand' : ''}\n\t\t\tsx={{\n\t\t\t\tposition: 'relative',\n\t\t\t\tpt: 1.5,\n\t\t\t\theight: maxHeight ? maxHeight : 'auto',\n\t\t\t\toverflowY: expand ? 'auto' : 'hidden',\n\t\t\t\tmsOverflowStyle: 'none',\n\t\t\t\tscrollbarWidth: 'none',\n\t\t\t\ttransition: 'height 0.3s ease',\n\t\t\t\tinterpolateSize: 'allow-keywords',\n\t\t\t\t'&::-webkit-scrollbar': {\n\t\t\t\t\tdisplay: 'none',\n\t\t\t\t},\n\t\t\t\t'&.expand': {\n\t\t\t\t\theight: 'auto',\n\t\t\t\t},\n\t\t\t\t'&::after': {\n\t\t\t\t\tcontent: '\"\"',\n\t\t\t\t\tposition: 'absolute',\n\t\t\t\t\tbottom: 0,\n\t\t\t\t\tleft: 0,\n\t\t\t\t\tright: 0,\n\t\t\t\t\theight: '40%',\n\t\t\t\t\tbackground: !maxHeight ? 'none' : 'linear-gradient(to top, white 10%, transparent)',\n\t\t\t\t\tdisplay: expand ? 'none' : 'block',\n\t\t\t\t\tpointerEvents: 'none',\n\t\t\t\t},\n\t\t\t}}\n\t\t>\n\t\t\t{children}\n\t\t</Stack>\n\t);\n}\n"], "names": ["CardBody", "children", "maxHeight", "expand", "<PERSON><PERSON>"], "mappings": ";;;AASe,SAASA,SAAS,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,SAAS,KAAK,EAAiB;IACtF,OAAO,WAAP,GACC,IAACC,OAAKA;QACL,WAAWD,SAAS,WAAW;QAC/B,IAAI;YACH,UAAU;YACV,IAAI;YACJ,QAAQD,YAAYA,YAAY;YAChC,WAAWC,SAAS,SAAS;YAC7B,iBAAiB;YACjB,gBAAgB;YAChB,YAAY;YACZ,iBAAiB;YACjB,wBAAwB;gBACvB,SAAS;YACV;YACA,YAAY;gBACX,QAAQ;YACT;YACA,YAAY;gBACX,SAAS;gBACT,UAAU;gBACV,QAAQ;gBACR,MAAM;gBACN,OAAO;gBACP,QAAQ;gBACR,YAAY,AAACD,YAAqB,oDAAT;gBACzB,SAASC,SAAS,SAAS;gBAC3B,eAAe;YAChB;QACD;kBAECF;;AAGJ"}