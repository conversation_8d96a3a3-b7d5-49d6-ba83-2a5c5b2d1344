import React from 'react';
import { ActionButtonProps } from '../../components/control/IconActions';
import { CheckboxProps } from '@mui/material/Checkbox';
export interface CardHeaderProps {
    title: React.ReactNode;
    description?: React.ReactNode;
    checked?: boolean;
    onChecked?: (event: React.ChangeEvent<HTMLInputElement>) => void;
    actions?: {
        [key: string]: ActionButtonProps;
    };
    menu?: {
        [key: string]: {
            label: string;
            onClick: () => void;
        };
    };
    components?: {
        title?: React.ComponentType;
        description?: React.ComponentType;
        checkbox?: React.ComponentType<CheckboxProps>;
    };
}
export default function CardHeader({ title, description, checked, onChecked, menu, components, actions, }: CardHeaderProps): import("react/jsx-runtime").JSX.Element;
