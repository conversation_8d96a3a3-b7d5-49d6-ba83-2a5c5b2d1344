import { jsx } from "react/jsx-runtime";
import "react";
import Card from "@mui/material/Card";
function Card_Card({ children, sx, ...props }) {
    return /*#__PURE__*/ jsx(Card, {
        sx: [
            {
                borderRadius: '16px',
                boxShadow: 'none',
                border: '1px solid',
                borderColor: 'gray.50',
                padding: '24px'
            },
            ...Array.isArray(sx) ? sx : [
                sx
            ]
        ],
        ...props,
        children: children
    });
}
export { Card_Card as default };

//# sourceMappingURL=Card.js.map