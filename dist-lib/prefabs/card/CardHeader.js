import { jsx, jsxs } from "react/jsx-runtime";
import "react";
import IconActions from "../../components/control/IconActions.js";
import Checkbox from "@mui/material/Checkbox";
import Grid from "@mui/material/Grid";
import Typography from "@mui/material/Typography";
import ContextMenu from "../../components/control/ContextMenu.js";
function CardHeader({ title, description, checked, onChecked, menu, components, actions }) {
    return /*#__PURE__*/ jsxs(Grid, {
        container: true,
        columnSpacing: 0.5,
        children: [
            /*#__PURE__*/ jsx(Grid, {
                size: 'grow',
                sx: {
                    display: 'flex',
                    alignItems: 'center',
                    overflow: 'hidden'
                },
                children: (null == components ? void 0 : components.title) ? /*#__PURE__*/ jsx(components.title, {}) : /*#__PURE__*/ jsx(Typography, {
                    variant: "subtitle-sm",
                    sx: {
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                        whiteSpace: 'nowrap'
                    },
                    children: title
                })
            }),
            actions && /*#__PURE__*/ jsx(Grid, {
                size: 'auto',
                sx: {
                    display: 'flex',
                    justifyContent: 'flex-end'
                },
                children: /*#__PURE__*/ jsx(IconActions, {
                    actions: actions
                })
            }),
            menu && /*#__PURE__*/ jsx(Grid, {
                size: 'auto',
                sx: {
                    display: 'flex',
                    justifyContent: 'flex-end'
                },
                children: /*#__PURE__*/ jsx(ContextMenu, {
                    menu: menu
                })
            }),
            (checked || onChecked) && /*#__PURE__*/ jsx(Grid, {
                size: 'auto',
                sx: {
                    display: 'flex',
                    justifyContent: 'flex-end'
                },
                children: (null == components ? void 0 : components.checkbox) ? /*#__PURE__*/ jsx(components.checkbox, {
                    checked: checked,
                    onChange: onChecked
                }) : /*#__PURE__*/ jsx(Checkbox, {
                    sx: {
                        '&.MuiCheckbox-root': {
                            width: '36px',
                            height: '36px',
                            borderRadius: 2
                        }
                    },
                    checked: checked,
                    onChange: onChecked
                })
            }),
            /*#__PURE__*/ jsx(Grid, {
                size: 12,
                children: (null == components ? void 0 : components.description) ? /*#__PURE__*/ jsx(components.description, {}) : /*#__PURE__*/ jsx(Typography, {
                    variant: "body-sm",
                    color: "gray.500",
                    children: description
                })
            })
        ]
    });
}
export { CardHeader as default };

//# sourceMappingURL=CardHeader.js.map