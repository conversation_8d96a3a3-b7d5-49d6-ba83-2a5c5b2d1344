import React from 'react';
interface PageHeaderProps {
    title: string;
    description: string;
    avatar?: {
        label: string;
        src: string;
    };
    breadcrumbs?: {
        label: string;
        href: string;
    }[];
    meta?: React.ReactNode;
    hiddenTitle?: boolean;
    docs?: string;
    sx?: any;
}
export default function PageHeader({ title, description, avatar, breadcrumbs, meta, hiddenTitle, docs, sx }: PageHeaderProps): import("react/jsx-runtime").JSX.Element;
export {};
