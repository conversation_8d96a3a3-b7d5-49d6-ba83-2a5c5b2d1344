import { ContainerProps } from '@mui/material';
import PageHeader from './PageHeader';
import { StackProps } from '@mui/material/Stack';
interface PageProps extends ContainerProps {
}
declare function Page({ maxWidth, sx, children, ...props }: PageProps): import("react/jsx-runtime").JSX.Element;
declare namespace Page {
    var Header: typeof PageHeader;
}
export default Page;
export declare function PageBody({ children, sx, ...props }: StackProps): import("react/jsx-runtime").JSX.Element;
