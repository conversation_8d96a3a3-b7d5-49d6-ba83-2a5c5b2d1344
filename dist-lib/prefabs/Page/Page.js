import { jsx } from "react/jsx-runtime";
import { Container } from "@mui/material";
import PageHeader from "./PageHeader.js";
import Stack from "@mui/material/Stack";
function Page({ maxWidth = 'xl', sx, children, ...props }) {
    return /*#__PURE__*/ jsx(Container, {
        maxWidth: maxWidth,
        sx: [
            {
                my: {
                    xs: '80px',
                    md: 6
                }
            },
            ...Array.isArray(sx) ? sx : [
                sx
            ]
        ],
        ...props,
        children: children
    });
}
Page.Header = PageHeader;
function PageBody({ children, sx, ...props }) {
    return /*#__PURE__*/ jsx(Stack, {
        sx: [
            {
                py: 4,
                gap: 2
            },
            ...Array.isArray(sx) ? sx : [
                sx
            ]
        ],
        ...props,
        children: children
    });
}
export { PageBody, Page as default };

//# sourceMappingURL=Page.js.map