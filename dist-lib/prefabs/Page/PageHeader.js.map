{"version": 3, "file": "prefabs/Page/PageHeader.js", "sources": ["webpack://dcl-prefab-components/./src/lib/prefabs/Page/PageHeader.tsx"], "sourcesContent": ["import Grid from \"@mui/material/Grid\";\nimport { Avatar, Typography } from '@mui/material';\nimport { Helmet } from 'react-helmet-async';\nimport React from 'react';\n\ninterface PageHeaderProps {\n\ttitle: string;\n\tdescription: string;\n\tavatar?: {\n\t\tlabel: string;\n\t\tsrc: string;\n\t};\n\tbreadcrumbs?: {\n\t\tlabel: string;\n\t\thref: string;\n\t}[];\n\tmeta?: React.ReactNode;\n\thiddenTitle?: boolean;\n\tdocs?: string;\n\tsx?: any;\n}\n\nexport default function PageHeader({ title, description, avatar, breadcrumbs, meta, hiddenTitle = false, docs, sx } : PageHeaderProps) {\n\treturn (\n\t\t<Grid\n\t\t\tsx={[\n\t\t\t\t{\n\t\t\t\t\tmb: 1,\n\t\t\t\t},\n\t\t\t\t...(Array.isArray(sx) ? sx : [sx]),\n\t\t\t]}\n\t\t\tcontainer\n\t\t\tcolumnSpacing={2}>\n\t\t\t<Helmet>\n\t\t\t\t<title>{title}</title>\n\t\t\t\t<meta\n\t\t\t\t\tname=\"description\"\n\t\t\t\t\tcontent={description}\n\t\t\t\t/>\n\t\t\t\t<meta\n\t\t\t\t\tname=\"og:title\"\n\t\t\t\t\tcontent={title}\n\t\t\t\t/>\n\t\t\t\t<meta\n\t\t\t\t\tname=\"og:description\"\n\t\t\t\t\tcontent={description}\n\t\t\t\t/>\n\t\t\t</Helmet>\n\t\t\t<Grid\n\t\t\t\tsize={10}\n\t\t\t\tsx={{ display: 'flex', alignItems: 'center' }}>\n\t\t\t\t{/*<PageBreadcrumbs breadcrumbs={breadcrumbs} />*/}\n\t\t\t</Grid>\n\t\t\t<Grid\n\t\t\t\tsize={2}\n\t\t\t\tsx={{ display: 'flex', alignItems: 'center', justifyContent: 'flex-end', gap: 2 }}>\n\t\t\t\t{meta}\n\t\t\t\t{/*<DocumentationLink docs={docs} />*/}\n\t\t\t</Grid>\n\t\t\t<Grid\n\t\t\t\tsize={12}\n\t\t\t\tsx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\n\t\t\t\t{avatar && (\n\t\t\t\t\t<Avatar\n\t\t\t\t\t\tclassName=\"page-avatar\"\n\t\t\t\t\t\talt={avatar?.label}\n\t\t\t\t\t\tsrc={avatar?.src}\n\t\t\t\t\t\tsx={{\n\t\t\t\t\t\t\twidth: 64,\n\t\t\t\t\t\t\theight: 64,\n\t\t\t\t\t\t\tbgcolor: 'primary.50',\n\t\t\t\t\t\t\tcolor: 'primary.600',\n\t\t\t\t\t\t\tfontWeight: 500,\n\t\t\t\t\t\t\tfontSize: '24px',\n\t\t\t\t\t\t\tlineHeight: '32px',\n\t\t\t\t\t\t}}>\n\t\t\t\t\t\t{avatar?.label}\n\t\t\t\t\t</Avatar>\n\t\t\t\t)}\n\t\t\t\t{!hiddenTitle && (\n\t\t\t\t\t<Typography\n\t\t\t\t\t\tclassName=\"page-title\"\n\t\t\t\t\t\tvariant=\"title\"\n\t\t\t\t\t\tsx={{\n\t\t\t\t\t\t\tfontSize: '30px',\n\t\t\t\t\t\t\tcolor: 'gray.800',\n\t\t\t\t\t\t\tfontWeight: 600,\n\t\t\t\t\t\t\tletterSpacing: '0.03em',\n\t\t\t\t\t\t}}>\n\t\t\t\t\t\t{title}\n\t\t\t\t\t</Typography>\n\t\t\t\t)}\n\t\t\t</Grid>\n\t\t\t<Grid size={12}>\n\t\t\t\t<Typography\n\t\t\t\t\tclassName=\"page-description\"\n\t\t\t\t\tvariant=\"body\"\n\t\t\t\t\tsx={{\n\t\t\t\t\t\tcolor: 'gray.500',\n\t\t\t\t\t\tmt: 1,\n\t\t\t\t\t}}>\n\t\t\t\t\t{description}\n\t\t\t\t</Typography>\n\t\t\t</Grid>\n\t\t</Grid>\n\t)\n}\n"], "names": ["<PERSON><PERSON><PERSON><PERSON>", "title", "description", "avatar", "breadcrumbs", "meta", "hiddenTitle", "docs", "sx", "Grid", "Array", "<PERSON><PERSON><PERSON>", "Avatar", "Typography"], "mappings": ";;;;;AAsBe,SAASA,WAAW,EAAEC,KAAK,EAAEC,WAAW,EAAEC,MAAM,EAAEC,WAAW,EAAEC,IAAI,EAAEC,cAAc,KAAK,EAAEC,IAAI,EAAEC,EAAE,EAAoB;IACpI,OAAO,WAAP,GACC,KAACC,MAAIA;QACJ,IAAI;YACH;gBACC,IAAI;YACL;eACIC,MAAM,OAAO,CAACF,MAAMA,KAAK;gBAACA;aAAG;SACjC;QACD,WAAS;QACT,eAAe;;0BACf,KAACG,QAAMA;;kCACN,IAAC;kCAAOV;;kCACR,IAAC;wBACA,MAAK;wBACL,SAASC;;kCAEV,IAAC;wBACA,MAAK;wBACL,SAASD;;kCAEV,IAAC;wBACA,MAAK;wBACL,SAASC;;;;0BAGX,IAACO,MAAIA;gBACJ,MAAM;gBACN,IAAI;oBAAE,SAAS;oBAAQ,YAAY;gBAAS;;0BAG7C,IAACA,MAAIA;gBACJ,MAAM;gBACN,IAAI;oBAAE,SAAS;oBAAQ,YAAY;oBAAU,gBAAgB;oBAAY,KAAK;gBAAE;0BAC/EJ;;0BAGF,KAACI,MAAIA;gBACJ,MAAM;gBACN,IAAI;oBAAE,SAAS;oBAAQ,YAAY;oBAAU,KAAK;gBAAE;;oBACnDN,UAAU,WAAVA,GACA,IAACS,QAAMA;wBACN,WAAU;wBACV,KAAKT,QAAAA,SAAAA,KAAAA,IAAAA,OAAQ,KAAK;wBAClB,KAAKA,QAAAA,SAAAA,KAAAA,IAAAA,OAAQ,GAAG;wBAChB,IAAI;4BACH,OAAO;4BACP,QAAQ;4BACR,SAAS;4BACT,OAAO;4BACP,YAAY;4BACZ,UAAU;4BACV,YAAY;wBACb;kCACCA,QAAAA,SAAAA,KAAAA,IAAAA,OAAQ,KAAK;;oBAGf,CAACG,eAAe,WAAfA,GACD,IAACO,YAAUA;wBACV,WAAU;wBACV,SAAQ;wBACR,IAAI;4BACH,UAAU;4BACV,OAAO;4BACP,YAAY;4BACZ,eAAe;wBAChB;kCACCZ;;;;0BAIJ,IAACQ,MAAIA;gBAAC,MAAM;0BACX,kBAACI,YAAUA;oBACV,WAAU;oBACV,SAAQ;oBACR,IAAI;wBACH,OAAO;wBACP,IAAI;oBACL;8BACCX;;;;;AAKN"}