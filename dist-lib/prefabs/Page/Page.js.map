{"version": 3, "file": "prefabs/Page/Page.js", "sources": ["webpack://dcl-prefab-components/./src/lib/prefabs/Page/Page.tsx"], "sourcesContent": ["import { Container, ContainerProps} from '@mui/material';\nimport PageHeader from './PageHeader';\n\nimport Stack from '@mui/material/Stack';\nimport { StackProps } from '@mui/material/Stack';\n\ninterface PageProps extends ContainerProps {}\n\nexport default function Page({ maxWidth = 'xl', sx, children, ...props } : PageProps) {\n\treturn (\n\t\t<Container\n\t\t\tmaxWidth={maxWidth}\n\t\t\tsx={[\n\t\t\t\t{\n\t\t\t\t\tmy: {\n\t\t\t\t\t\txs: '80px',\n\t\t\t\t\t\tmd: 6,\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t\t...(Array.isArray(sx) ? sx : [sx]),\n\t\t\t]}\n\t\t\t{...props}>\n\t\t\t{children}\n\t\t</Container>\n\t)\n}\n\nPage.Header = PageHeader;\n\nexport function PageBody({ children, sx, ...props }: StackProps) {\n\treturn (\n\t\t<Stack\n\t\t\tsx={[\n\t\t\t\t{\n\t\t\t\t\tpy: 4,\n\t\t\t\t\tgap: 2,\n\t\t\t\t},\n\t\t\t\t...(Array.isArray(sx) ? sx : [sx]),\n\t\t\t]}\n\t\t\t{...props}>\n\t\t\t{children}\n\t\t</Stack>\n\t)\n}\n"], "names": ["Page", "max<PERSON><PERSON><PERSON>", "sx", "children", "props", "Container", "Array", "<PERSON><PERSON><PERSON><PERSON>", "PageBody", "<PERSON><PERSON>"], "mappings": ";;;;AAQe,SAASA,KAAK,EAAEC,WAAW,IAAI,EAAEC,EAAE,EAAEC,QAAQ,EAAE,GAAGC,OAAmB;IACnF,OAAO,WAAP,GACC,IAACC,WAASA;QACT,UAAUJ;QACV,IAAI;YACH;gBACC,IAAI;oBACH,IAAI;oBACJ,IAAI;gBACL;YACD;eACIK,MAAM,OAAO,CAACJ,MAAMA,KAAK;gBAACA;aAAG;SACjC;QACA,GAAGE,KAAK;kBACRD;;AAGJ;AAEAH,KAAK,MAAM,GAAGO;AAEP,SAASC,SAAS,EAAEL,QAAQ,EAAED,EAAE,EAAE,GAAGE,OAAmB;IAC9D,OAAO,WAAP,GACC,IAACK,OAAKA;QACL,IAAI;YACH;gBACC,IAAI;gBACJ,KAAK;YACN;eACIH,MAAM,OAAO,CAACJ,MAAMA,KAAK;gBAACA;aAAG;SACjC;QACA,GAAGE,KAAK;kBACRD;;AAGJ"}