import { jsx, jsxs } from "react/jsx-runtime";
import Grid from "@mui/material/Grid";
import { Avatar, Typography } from "@mui/material";
import { Helmet } from "react-helmet-async";
import "react";
function PageHeader({ title, description, avatar, breadcrumbs, meta, hiddenTitle = false, docs, sx }) {
    return /*#__PURE__*/ jsxs(Grid, {
        sx: [
            {
                mb: 1
            },
            ...Array.isArray(sx) ? sx : [
                sx
            ]
        ],
        container: true,
        columnSpacing: 2,
        children: [
            /*#__PURE__*/ jsxs(Helmet, {
                children: [
                    /*#__PURE__*/ jsx("title", {
                        children: title
                    }),
                    /*#__PURE__*/ jsx("meta", {
                        name: "description",
                        content: description
                    }),
                    /*#__PURE__*/ jsx("meta", {
                        name: "og:title",
                        content: title
                    }),
                    /*#__PURE__*/ jsx("meta", {
                        name: "og:description",
                        content: description
                    })
                ]
            }),
            /*#__PURE__*/ jsx(Grid, {
                size: 10,
                sx: {
                    display: 'flex',
                    alignItems: 'center'
                }
            }),
            /*#__PURE__*/ jsx(Grid, {
                size: 2,
                sx: {
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'flex-end',
                    gap: 2
                },
                children: meta
            }),
            /*#__PURE__*/ jsxs(Grid, {
                size: 12,
                sx: {
                    display: 'flex',
                    alignItems: 'center',
                    gap: 2
                },
                children: [
                    avatar && /*#__PURE__*/ jsx(Avatar, {
                        className: "page-avatar",
                        alt: null == avatar ? void 0 : avatar.label,
                        src: null == avatar ? void 0 : avatar.src,
                        sx: {
                            width: 64,
                            height: 64,
                            bgcolor: 'primary.50',
                            color: 'primary.600',
                            fontWeight: 500,
                            fontSize: '24px',
                            lineHeight: '32px'
                        },
                        children: null == avatar ? void 0 : avatar.label
                    }),
                    !hiddenTitle && /*#__PURE__*/ jsx(Typography, {
                        className: "page-title",
                        variant: "title",
                        sx: {
                            fontSize: '30px',
                            color: 'gray.800',
                            fontWeight: 600,
                            letterSpacing: '0.03em'
                        },
                        children: title
                    })
                ]
            }),
            /*#__PURE__*/ jsx(Grid, {
                size: 12,
                children: /*#__PURE__*/ jsx(Typography, {
                    className: "page-description",
                    variant: "body",
                    sx: {
                        color: 'gray.500',
                        mt: 1
                    },
                    children: description
                })
            })
        ]
    });
}
export { PageHeader as default };

//# sourceMappingURL=PageHeader.js.map