import { jsx } from "react/jsx-runtime";
import Stack from "@mui/material/Stack";
import { ButtonIcon } from "../buttons/ButtonIcon.js";
import "react";
import Tooltip from "@mui/material/Tooltip";
function IconActions({ actions }) {
    return /*#__PURE__*/ jsx(Stack, {
        direction: "row",
        gap: 0.5,
        children: Object.keys(actions).map((key)=>/*#__PURE__*/ jsx(Tooltip, {
                title: actions[key].tooltip,
                children: /*#__PURE__*/ jsx(ButtonIcon, {
                    ...actions[key],
                    children: actions[key].icon
                }, key)
            }, key))
    });
}
export { IconActions as default };

//# sourceMappingURL=IconActions.js.map