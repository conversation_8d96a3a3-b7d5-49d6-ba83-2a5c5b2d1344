{"version": 3, "file": "components/control/ContextMenu.js", "sources": ["webpack://dcl-prefab-components/./src/lib/components/control/ContextMenu.tsx"], "sourcesContent": ["import { <PERSON><PERSON><PERSON><PERSON>on, <PERSON>u, MenuItem } from '@mui/material';\nimport React, { SetStateAction } from 'react';\nimport MoreVertIcon from '@mui/icons-material/MoreVert';\nimport { ButtonIcon } from '../buttons/ButtonIcon';\n\n\nexport interface ContextMenuProps {\n\tmenu: {\n\t\t[key: string]: {\n\t\t\tlabel: string;\n\t\t\tonClick: () => void;\n\t\t};\n\t};\n}\n\nexport default function ContextMenu({ menu }: ContextMenuProps) {\n\tconst [anchorEl, setAnchorEl] = React.useState(null);\n\tconst open = Boolean(anchorEl);\n\tconst handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {\n\t\tsetAnchorEl(event.currentTarget as unknown as SetStateAction<null>);\n\t};\n\tconst handleClose = () => {\n\t\tsetAnchorEl(null);\n\t};\n\t\n\treturn (\n\t\t<>\n\t\t\t<ButtonIcon\n\t\t\t\tid=\"basic-button\"\n\t\t\t\taria-controls={open ? 'basic-menu' : undefined}\n\t\t\t\taria-haspopup=\"true\"\n\t\t\t\taria-expanded={open ? 'true' : undefined}\n\t\t\t\tonClick={handleClick}\n\t\t\t>\n\t\t\t\t<MoreVertIcon />\n\t\t\t</ButtonIcon>\n\t\t\t<Menu\n\t\t\t\tid=\"basic-menu\"\n\t\t\t\tanchorEl={anchorEl}\n\t\t\t\topen={open}\n\t\t\t\tonClose={handleClose}\n\t\t\t\tslotProps={{\n\t\t\t\t\tlist: {\n\t\t\t\t\t\t'aria-labelledby': 'basic-button',\n\t\t\t\t\t},\n\t\t\t\t}}\n\t\t\t>\n\t\t\t\t{Object.keys(menu).map((key) => (\n\t\t\t\t\t<MenuItem key={key} onClick={menu[key].onClick}>\n\t\t\t\t\t\t{menu[key].label}\n\t\t\t\t\t</MenuItem>\n\t\t\t\t))}\n\t\t\t</Menu>\n\t\t</>\n\t);\n}\n"], "names": ["ContextMenu", "menu", "anchorEl", "setAnchorEl", "React", "open", "Boolean", "handleClick", "event", "handleClose", "ButtonIcon", "undefined", "MoreVertIcon", "<PERSON><PERSON>", "Object", "key", "MenuItem"], "mappings": ";;;;;AAee,SAASA,YAAY,EAAEC,IAAI,EAAoB;IAC7D,MAAM,CAACC,UAAUC,YAAY,GAAGC,MAAAA,QAAc,CAAC;IAC/C,MAAMC,OAAOC,QAAQJ;IACrB,MAAMK,cAAc,CAACC;QACpBL,YAAYK,MAAM,aAAa;IAChC;IACA,MAAMC,cAAc;QACnBN,YAAY;IACb;IAEA,OAAO,WAAP,GACC;;0BACC,IAACO,YAAUA;gBACV,IAAG;gBACH,iBAAeL,OAAO,eAAeM;gBACrC,iBAAc;gBACd,iBAAeN,OAAO,SAASM;gBAC/B,SAASJ;0BAET,kBAACK,UAAYA,CAAAA;;0BAEd,IAACC,MAAIA;gBACJ,IAAG;gBACH,UAAUX;gBACV,MAAMG;gBACN,SAASI;gBACT,WAAW;oBACV,MAAM;wBACL,mBAAmB;oBACpB;gBACD;0BAECK,OAAO,IAAI,CAACb,MAAM,GAAG,CAAC,CAACc,MAAAA,WAAAA,GACvB,IAACC,UAAQA;wBAAW,SAASf,IAAI,CAACc,IAAI,CAAC,OAAO;kCAC5Cd,IAAI,CAACc,IAAI,CAAC,KAAK;uBADFA;;;;AAOpB"}