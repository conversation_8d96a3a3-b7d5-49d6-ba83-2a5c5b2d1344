import { Fragment, jsx, jsxs } from "react/jsx-runtime";
import { Menu, MenuItem } from "@mui/material";
import react from "react";
import MoreVert from "@mui/icons-material/MoreVert";
import { ButtonIcon } from "../buttons/ButtonIcon.js";
function ContextMenu({ menu }) {
    const [anchorEl, setAnchorEl] = react.useState(null);
    const open = Boolean(anchorEl);
    const handleClick = (event)=>{
        setAnchorEl(event.currentTarget);
    };
    const handleClose = ()=>{
        setAnchorEl(null);
    };
    return /*#__PURE__*/ jsxs(Fragment, {
        children: [
            /*#__PURE__*/ jsx(ButtonIcon, {
                id: "basic-button",
                "aria-controls": open ? 'basic-menu' : void 0,
                "aria-haspopup": "true",
                "aria-expanded": open ? 'true' : void 0,
                onClick: handleClick,
                children: /*#__PURE__*/ jsx(MoreVert, {})
            }),
            /*#__PURE__*/ jsx(Menu, {
                id: "basic-menu",
                anchorEl: anchorEl,
                open: open,
                onClose: handleClose,
                slotProps: {
                    list: {
                        'aria-labelledby': 'basic-button'
                    }
                },
                children: Object.keys(menu).map((key)=>/*#__PURE__*/ jsx(MenuItem, {
                        onClick: menu[key].onClick,
                        children: menu[key].label
                    }, key))
            })
        ]
    });
}
export { ContextMenu as default };

//# sourceMappingURL=ContextMenu.js.map