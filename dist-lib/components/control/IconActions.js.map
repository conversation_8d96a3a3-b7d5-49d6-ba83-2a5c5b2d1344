{"version": 3, "file": "components/control/IconActions.js", "sources": ["webpack://dcl-prefab-components/./src/lib/components/control/IconActions.tsx"], "sourcesContent": ["import Stack from '@mui/material/Stack';\nimport { ButtonIcon } from '../buttons/ButtonIcon';\nimport React from 'react';\nimport Tooltip from '@mui/material/Tooltip';\nimport { IconButtonProps } from '@mui/material/IconButton';\n\nexport interface ActionButtonProps extends IconButtonProps {\n\ticon: React.ReactNode;\n\ttooltip: string;\n}\n\nexport interface IconActionsProps {\n\tactions: {\n\t\t[key: string]: ActionButtonProps;\n\t};\n}\n\nexport default function IconActions({ actions }: IconActionsProps) {\n\t\n\treturn (\n\t\t<Stack\n\t\t\tdirection=\"row\"\n\t\t\tgap={0.5}\n\t\t>\n\t\t\t{Object.keys(actions).map((key) => (\n\t\t\t\t<Tooltip\n\t\t\t\t\tkey={key}\n\t\t\t\t\ttitle={actions[key].tooltip}\n\t\t\t\t>\n\t\t\t\t\t<ButtonIcon\n\t\t\t\t\t\tkey={key}\n\t\t\t\t\t\t{...actions[key]}\n\t\t\t\t\t>\n\t\t\t\t\t\t{actions[key].icon}\n\t\t\t\t\t</ButtonIcon>\n\t\t\t\t</Tooltip>\n\t\t\t))}\n\t\t</Stack>\n\t);\n}\n"], "names": ["IconActions", "actions", "<PERSON><PERSON>", "Object", "key", "<PERSON><PERSON><PERSON>", "ButtonIcon"], "mappings": ";;;;;AAiBe,SAASA,YAAY,EAAEC,OAAO,EAAoB;IAEhE,OAAO,WAAP,GACC,IAACC,OAAKA;QACL,WAAU;QACV,KAAK;kBAEJC,OAAO,IAAI,CAACF,SAAS,GAAG,CAAC,CAACG,MAAAA,WAAAA,GAC1B,IAACC,SAAOA;gBAEP,OAAOJ,OAAO,CAACG,IAAI,CAAC,OAAO;0BAE3B,kBAACE,YAAUA;oBAET,GAAGL,OAAO,CAACG,IAAI;8BAEfH,OAAO,CAACG,IAAI,CAAC,IAAI;mBAHbA;eAJDA;;AAaV"}