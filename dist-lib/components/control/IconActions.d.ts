import React from 'react';
import { IconButtonProps } from '@mui/material/IconButton';
export interface ActionButtonProps extends IconButtonProps {
    icon: React.ReactNode;
    tooltip: string;
}
export interface IconActionsProps {
    actions: {
        [key: string]: ActionButtonProps;
    };
}
export default function IconActions({ actions }: IconActionsProps): import("react/jsx-runtime").JSX.Element;
