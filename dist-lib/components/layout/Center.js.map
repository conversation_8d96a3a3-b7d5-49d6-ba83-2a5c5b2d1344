{"version": 3, "file": "components/layout/Center.js", "sources": ["webpack://dcl-prefab-components/./src/lib/components/layout/Center.tsx"], "sourcesContent": ["import { Container } from '@mui/material';\nimport React from 'react';\n\n\ninterface CenterProps {\n\tclassName?: string;\n\tchildren: React.ReactNode;\n\tsx?: any;\n}\n\nexport default function Center({ children, sx, className }: CenterProps) {\n\treturn (\n\t\t<Container\n\t\t\tclassName={className}\n\t\t\tsx={[\n\t\t\t\t{\n\t\t\t\t\tdisplay: 'flex',\n\t\t\t\t\tflexDirection: 'column',\n\t\t\t\t\tjustifyContent: 'center',\n\t\t\t\t\talignItems: 'center',\n\t\t\t\t\tminHeight: '100vh',\n\t\t\t\t},\n\t\t\t\t...(Array.isArray(sx) ? sx : [sx]),\n\t\t\t]}\n\t\t>\n\t\t\t{children}\n\t\t</Container>\n\t);\n}\n"], "names": ["Center", "children", "sx", "className", "Container", "Array"], "mappings": ";;;AAUe,SAASA,OAAO,EAAEC,QAAQ,EAAEC,EAAE,EAAEC,SAAS,EAAe;IACtE,OAAO,WAAP,GACC,IAACC,WAASA;QACT,WAAWD;QACX,IAAI;YACH;gBACC,SAAS;gBACT,eAAe;gBACf,gBAAgB;gBAChB,YAAY;gBACZ,WAAW;YACZ;eACIE,MAAM,OAAO,CAACH,MAAMA,KAAK;gBAACA;aAAG;SACjC;kBAEAD;;AAGJ"}