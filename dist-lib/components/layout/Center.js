import { jsx } from "react/jsx-runtime";
import { Container } from "@mui/material";
import "react";
function Center({ children, sx, className }) {
    return /*#__PURE__*/ jsx(Container, {
        className: className,
        sx: [
            {
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'center',
                alignItems: 'center',
                minHeight: '100vh'
            },
            ...Array.isArray(sx) ? sx : [
                sx
            ]
        ],
        children: children
    });
}
export { Center as default };

//# sourceMappingURL=Center.js.map