{"version": 3, "file": "components/buttons/ButtonIcon.js", "sources": ["webpack://dcl-prefab-components/./src/lib/components/buttons/ButtonIcon.tsx"], "sourcesContent": ["import { IconButton, styled } from '@mui/material';\n\nexport const ButtonIcon = styled(IconButton) ({\n\t'&.MuiIconButton-root': {\n\t\theight: '36px',\n\t\twidth: '36px',\n\t\tborderRadius: 8,\n\t}\n});\n"], "names": ["ButtonIcon", "styled", "IconButton"], "mappings": ";AAEO,MAAMA,aAAaC,OAAOC,YAAa;IAC7C,wBAAwB;QACvB,QAAQ;QACR,OAAO;QACP,cAAc;IACf;AACD"}